<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->text('more_info')->nullable();
            $table->unsignedBigInteger('from_user_id')->index();
            $table->foreign('from_user_id')->references('id')->on('users')->onDelete('set null');
            $table->unsignedBigInteger('to_user_id')->index();
            $table->foreign('to_user_id')->references('id')->on('users')->onDelete('set null');
            $table->unsignedBigInteger('report_reason_id')->index();
            $table->foreign('report_reason_id')->references('id')->on('reports_reasons')->onDelete('restrict');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
