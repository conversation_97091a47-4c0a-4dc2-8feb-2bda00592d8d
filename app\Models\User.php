<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone',
        'email',
        'first_contact_phone',
        'second_contact_phone',
        'is_admin',
        'status',
        'is_donor',
        'gender',
        'birthdate',
        'verification_code',
        'verification_code_expired_at',
        'email_verified_at',
        'fcm',
        'password',
        'more_info',
        'blood_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'verification_code',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'verification_code_expired_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
            'status' => 'boolean',
            'is_donor' => 'boolean',
            'gender' => 'boolean',
            'birthdate' => 'date',
        ];
    }

    /**
     * Get the blood type that belongs to this user
     */
    public function blood()
    {
        return $this->belongsTo(Blood::class);
    }

    /**
     * Get all diseases associated with this user (many-to-many relationship)
     */
    public function diseases()
    {
        return $this->belongsToMany(Disease::class, 'disease_users')
                    ->withTimestamps();
    }

    /**
     * Get all disease_users pivot records for this user
     */
    public function diseaseUsers()
    {
        return $this->hasMany(DiseaseUser::class);
    }

    /**
     * Get all diseases created by this user
     */
    public function createdDiseases()
    {
        return $this->hasMany(Disease::class);
    }

    /**
     * Get all health centers associated with this user (many-to-many relationship)
     */
    public function healthCenters()
    {
        return $this->belongsToMany(HealthCenter::class, 'health_center_users')
                    ->withTimestamps();
    }

    /**
     * Get all health_center_users pivot records for this user
     */
    public function healthCenterUsers()
    {
        return $this->hasMany(HealthCenterUser::class);
    }

    /**
     * Get all health centers managed by this user
     */
    public function managedHealthCenters()
    {
        return $this->hasMany(HealthCenter::class);
    }

    /**
     * Get all reports made by this user
     */
    public function madeReports()
    {
        return $this->hasMany(Report::class, 'from_user_id');
    }

    /**
     * Get all reports against this user
     */
    public function receivedReports()
    {
        return $this->hasMany(Report::class, 'to_user_id');
    }

    /**
     * Get all orders made by this user (as requester)
     */
    public function madeOrders()
    {
        return $this->hasMany(Order::class, 'from_user_id');
    }

    /**
     * Get all orders received by this user (as donor)
     */
    public function receivedOrders()
    {
        return $this->hasMany(Order::class, 'to_user_id');
    }

    /**
     * Get all orders quantity created by this user
     */
    public function ordersQuantity()
    {
        return $this->hasMany(OrdersQuantity::class);
    }

    /**
     * Get all orders quantity where this user is a donor (many-to-many relationship)
     */
    public function donorOrdersQuantity()
    {
        return $this->belongsToMany(OrdersQuantity::class, 'orders_quantity_users', 'donor_id', 'orders_quantity_id')
                    ->withPivot('quantity', 'status')
                    ->withTimestamps();
    }

    /**
     * Get all orders_quantity_users pivot records for this user as donor
     */
    public function ordersQuantityUsers()
    {
        return $this->hasMany(OrdersQuantityUser::class, 'donor_id');
    }
}
