<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\BloodController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\HealthCenterController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Public blood types (for registration)
Route::get('/blood-types', [BloodController::class, 'index']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/profile', [AuthController::class, 'profile']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);

    // Blood routes
    Route::apiResource('blood', BloodController::class);
    Route::get('/blood/{blood}/donors', [BloodController::class, 'donors']);

    // Order routes
    Route::apiResource('orders', OrderController::class);
    Route::get('/orders/sent/list', [OrderController::class, 'sent']);
    Route::get('/orders/received/list', [OrderController::class, 'received']);

    // Health Center routes
    Route::apiResource('health-centers', HealthCenterController::class);
    Route::get('/health-centers/managed/list', [HealthCenterController::class, 'managed']);
    Route::get('/health-centers/associated/list', [HealthCenterController::class, 'associated']);

    // User info route
    Route::get('/user', function (Request $request) {
        return response()->json([
            'success' => true,
            'data' => $request->user()->load('blood')
        ]);
    });
});
